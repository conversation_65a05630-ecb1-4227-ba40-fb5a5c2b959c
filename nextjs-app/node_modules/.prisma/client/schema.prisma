// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                  String   @id @default(cuid())
  username            String   @unique
  name                String
  password            String
  role                String   @default("student") // "student" | "admin"
  forcePasswordChange Boolean  @default(false)
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  // Relations
  profile       Profile?
  tasks         Task[]
  leaveRequests LeaveRequest[]

  @@map("users")
}

model Profile {
  id                  String   @id @default(cuid())
  userId              String   @unique
  gender              String?
  age                 String?
  studyStatus         String?
  studyStatusOther    String?
  mathType            String?
  mathTypeOther       String?
  targetScore         String?
  dailyHours          String?
  gaokaoYear          String   @default("未参加")
  gaokaoProvince      String?
  gaokaoScore         String?
  gradExamYear        String   @default("未参加")
  gradExamMathType    String   @default("未考")
  gradExamScore       String?
  upgradeExamYear     String   @default("未参加")
  upgradeExamProvince String?
  upgradeExamMathType String   @default("未分类")
  upgradeExamScore    String?
  purchasedBooks      String?
  notes               String?
  isProfileSubmitted  Boolean  @default(false)
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("profiles")
}

model Task {
  id             String   @id @default(cuid())
  userId         String
  date           String // Format: YYYY-MM-DD
  title          String
  type           String // "练习" | "复习" | "预习" | "其他" | "休息" | "leave"
  completed      Boolean  @default(false)
  durationHour   Int?
  durationMinute Int?
  proofUrl       String?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("tasks")
}

model LeaveRequest {
  id        String   @id @default(cuid())
  userId    String
  date      String // Format: YYYY-MM-DD
  reason    String?
  status    String   @default("approved") // "pending" | "approved" | "rejected"
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("leave_requests")
}

model SystemSetting {
  id        String   @id @default(cuid())
  key       String   @unique
  value     String
  updatedAt DateTime @updatedAt

  @@map("system_settings")
}
