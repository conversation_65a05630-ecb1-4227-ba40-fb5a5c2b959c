{"version": 3, "file": "FieldFormatter.js", "sourceRoot": "", "sources": ["../../../src/formatter/FieldFormatter.ts"], "names": [], "mappings": ";;;;;;AAAA,wEAAyC;AACzC,gEAAiC;AACjC,8EAA+C;AAI/C,MAAa,cAAc;IASvB,YAAmB,gBAAwC;QANnD,aAAQ,GAAoB,IAAI,CAAC;QAOrC,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,IAAI,gBAAgB,CAAC,OAAO,KAAK,IAAI,EAAE;YACnC,IAAI,CAAC,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC;SAC3C;QACD,IAAI,CAAC,cAAc,GAAG,IAAI,MAAM,CAAC,gBAAgB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAC9D,MAAM,aAAa,GAAG,IAAI,gBAAgB,CAAC,SAAS,GAAG,6BAAY,CAAC,gBAAgB,CAAC,YAAY,CAAC,SAAS,CAAC;QAC5G,IAAI,CAAC,aAAa,GAAG,IAAI,MAAM,CAAC,aAAa,CAAC,CAAC;IACnD,CAAC;IAED,IAAW,OAAO,CAAC,OAAiB;QAChC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC5B,CAAC;IAEO,WAAW,CAAC,UAAkB,EAAE,QAAiB;QACrD,MAAM,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC;QACvG,IAAI,0BAAS,CAAC,WAAW,CAAC,EAAE;YACxB,OAAO,WAAW,CAAC;SACtB;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;YAC5B,OAAO,WAAW,CAAC,UAAU,CAAC,CAAC;SAClC;QACD,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;YACxB,OAAO,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;SACjD;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,MAAM,CAAC,KAAa,EAAE,UAAkB,EAAE,QAAiB;QAC9D,MAAM,aAAa,GAAG,GAAG,sBAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QACxE,MAAM,EAAE,gBAAgB,EAAE,GAAG,IAAI,CAAC;QAClC,IAAI,gBAAgB,CAAC,KAAK,KAAK,EAAE,EAAE;YAC/B,MAAM,YAAY,GAAG,aAAa,CAAC,OAAO,CAAC,gBAAgB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;YAC1E,IAAI,YAAY,EAAE;gBACd,OAAO,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC;aACrG;SACJ;QACD,MAAM,mBAAmB,GAAG,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;QAC5E,IAAI,mBAAmB,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE;YAC/D,OAAO,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;SACzC;QACD,OAAO,aAAa,CAAC;IACzB,CAAC;IAEO,UAAU,CAAC,KAAa;QAC5B,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC;QACxC,OAAO,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,EAAE,CAAC;IACtC,CAAC;CACJ;AAzDD,wCAyDC"}