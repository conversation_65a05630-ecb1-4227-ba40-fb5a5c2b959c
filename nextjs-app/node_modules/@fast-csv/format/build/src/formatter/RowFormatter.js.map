{"version": 3, "file": "RowFormatter.js", "sourceRoot": "", "sources": ["../../../src/formatter/RowFormatter.ts"], "names": [], "mappings": ";;;;;;AAAA,0EAA2C;AAC3C,oEAAqC;AAErC,qDAAkD;AAClD,oCAAoH;AAMpH,MAAa,YAAY;IA0DrB,YAAmB,gBAAwC;QAFnD,aAAQ,GAAG,CAAC,CAAC;QAGjB,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,IAAI,CAAC,cAAc,GAAG,IAAI,+BAAc,CAAC,gBAAgB,CAAC,CAAC;QAE3D,IAAI,CAAC,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC;QACxC,IAAI,CAAC,kBAAkB,GAAG,gBAAgB,CAAC,kBAAkB,CAAC;QAC9D,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAC/B,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,EAAE;YACvB,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;SAC9C;QACD,IAAI,gBAAgB,CAAC,SAAS,EAAE;YAC5B,IAAI,CAAC,YAAY,GAAG,gBAAgB,CAAC,SAAS,CAAC;SAClD;IACL,CAAC;IAtEO,MAAM,CAAC,cAAc,CAAC,GAAQ;QAClC,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACpB,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;SACvD;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,MAAM,CAAC,UAAU,CAAC,GAAQ;QAC9B,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;IAC3D,CAAC;IAED,8BAA8B;IACtB,MAAM,CAAC,aAAa,CAAC,GAAQ;QACjC,IAAI,YAAY,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;YAClC,oEAAoE;YACpE,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,EAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;SACzC;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACpB,OAAO,GAAG,CAAC;SACd;QACD,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC5B,CAAC;IAED,wDAAwD;IAChD,MAAM,CAAC,eAAe,CAC1B,iBAA6C;QAE7C,IAAI,uBAAe,CAAC,iBAAiB,CAAC,EAAE;YACpC,OAAO,CAAC,GAAM,EAAE,EAA2B,EAAQ,EAAE;gBACjD,IAAI,cAAc,GAAG,IAAI,CAAC;gBAC1B,IAAI;oBACA,cAAc,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;iBAC3C;gBAAC,OAAO,CAAC,EAAE;oBACR,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;iBAChB;gBACD,OAAO,EAAE,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;YACpC,CAAC,CAAC;SACL;QACD,OAAO,CAAC,GAAM,EAAE,EAA2B,EAAQ,EAAE;YACjD,iBAAiB,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;QAC/B,CAAC,CAAC;IACN,CAAC;IA+BD,IAAW,YAAY,CAAC,iBAA6C;QACjE,IAAI,CAAC,2BAAU,CAAC,iBAAiB,CAAC,EAAE;YAChC,MAAM,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAC;SAC7D;QACD,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;IACzE,CAAC;IAEM,MAAM,CAAC,GAAM,EAAE,EAAwB;QAC1C,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,cAAoB,EAAQ,EAAE;YAC1D,IAAI,GAAG,EAAE;gBACL,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC;aAClB;YACD,IAAI,CAAC,GAAG,EAAE;gBACN,OAAO,EAAE,CAAC,IAAI,CAAC,CAAC;aACnB;YACD,MAAM,IAAI,GAAG,EAAE,CAAC;YAChB,IAAI,cAAc,EAAE;gBAChB,MAAM,EAAE,mBAAmB,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;gBAC3E,IAAI,IAAI,CAAC,kBAAkB,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;oBAC/D,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;oBAC7C,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;iBACjC;gBACD,IAAI,mBAAmB,EAAE;oBACrB,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;oBACnD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;iBACjD;aACJ;YACD,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,MAAM,CAAC,EAAwB;QAClC,MAAM,IAAI,GAAG,EAAE,CAAC;QAChB,6DAA6D;QAC7D,IAAI,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,EAAE;YACjE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACf,OAAO,EAAE,CAAC,IAAI,KAAK,CAAC,+EAA+E,CAAC,CAAC,CAAC;aACzG;YACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;SACrD;QACD,IAAI,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,EAAE;YAC9C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;SACjD;QACD,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC1B,CAAC;IAED,6EAA6E;IAC7E,gFAAgF;IACxE,YAAY,CAAC,GAAQ;QACzB,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,iFAAiF;YACjF,OAAO,EAAE,mBAAmB,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;SAC/D;QACD,MAAM,OAAO,GAAG,YAAY,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;QAChD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,OAAO,CAAC;QACtC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC1B,mDAAmD;YACnD,4BAA4B;YAC5B,OAAO,EAAE,mBAAmB,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;SACvD;QACD,6CAA6C;QAC7C,OAAO,EAAE,mBAAmB,EAAE,CAAC,wBAAO,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE,OAAO,EAAE,CAAC;IACpE,CAAC;IAED,uCAAuC;IAC/B,aAAa,CAAC,GAAQ;QAC1B,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;SAChD;QACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACrB,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAU,EAAE,CAAC,GAAG,CAAC,MAAM,CAAW,CAAC,CAAC;SACtE;QACD,IAAI,YAAY,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;YAClC,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAU,EAAE;gBAC1C,MAAM,GAAG,GAAI,GAAG,CAAC,CAAC,CAAuB,CAAC;gBAC1C,IAAI,GAAG,EAAE;oBACL,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;iBACjB;gBACD,OAAO,EAAE,CAAC;YACd,CAAC,CAAC,CAAC;SACN;QACD,+DAA+D;QAC/D,2BAA2B;QAC3B,IAAI,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC1D,OAAO,GAAG,CAAC;SACd;QACD,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAU,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D,CAAC;IAEO,eAAe,CAAC,GAAM,EAAE,EAA2B;QACvD,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,OAAO,EAAE,CAAC,IAAI,EAAG,GAAoB,CAAC,CAAC;SAC1C;QACD,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;IACvC,CAAC;IAEO,aAAa,CAAC,OAAiB,EAAE,YAAqB;QAC1D,MAAM,aAAa,GAAG,OAAO;aACxB,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,EAAU,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,YAAY,CAAC,CAAC;aAC7E,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAC3C,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;QACnB,IAAI,QAAQ,EAAE;YACV,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SACvE;QACD,OAAO,aAAa,CAAC;IACzB,CAAC;CACJ;AArLD,oCAqLC"}